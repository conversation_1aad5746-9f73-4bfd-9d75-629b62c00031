# Generated by Django 5.2.1 on 2025-06-10 15:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0002_add_contactform_permissions'),
    ]

    operations = [
        migrations.CreateModel(
            name='AppointmentForm',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('phone_number', models.CharField(max_length=20)),
                ('date_of_birth', models.DateField()),
                ('gender', models.Char<PERSON>ield(max_length=10)),
                ('type_of_service', models.CharField(max_length=100)),
                ('reason_for_seeking_therapy', models.TextField()),
                ('insurance', models.CharField(max_length=100)),
                ('preferred_callback_time', models.<PERSON><PERSON>ield()),
                ('how_did_you_hear_about_us', models.TextField()),
                ('additional_comments', models.TextField()),
                ('street_address', models.<PERSON>r<PERSON><PERSON>(max_length=100)),
                ('city', models.CharField(max_length=100)),
                ('state', models.CharField(max_length=100)),
                ('zip_code', models.CharField(max_length=20)),
                ('date', models.DateField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
