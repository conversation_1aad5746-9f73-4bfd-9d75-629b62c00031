from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsA<PERSON>enticated
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework_simplejwt.tokens import RefreshToken
from .serializers import UserRegistrationSerializer, CustomTokenObtainPairSerializer
from .services import AccountService


class CustomTokenObtainPairView(TokenObtainPairView):
    """
    Custom token obtain pair view that uses our custom serializer.
    """
    serializer_class = CustomTokenObtainPairSerializer


@api_view(['POST'])
def register_user(request):
    """
    Register a new user.

    Expected data:
    - email: User's email address
    - username: User's username
    - password: User's password
    - password2: Password confirmation
    - first_name: User's first name
    - last_name: User's last name
    """
    serializer = UserRegistrationSerializer(data=request.data)

    if serializer.is_valid():
        # Use the service to register the user
        service = AccountService()
        service_response = service.register_user(serializer.validated_data)

        return Response(
            {
                'success': service_response.success,
                'message': service_response.message,
                'data': service_response.data
            },
            status=service_response.status
        )
    else:
        return Response(
            {
                'success': False,
                'message': 'Invalid data provided.',
                'errors': serializer.errors
            },
            status=status.HTTP_400_BAD_REQUEST
        )


@api_view(['POST'])
def login_user(request):
    """
    Login a user and return tokens.

    Expected data:
    - email: User's email address
    - password: User's password
    """
    email = request.data.get('email')
    password = request.data.get('password')

    if not email or not password:
        return Response(
            {
                'success': False,
                'message': 'Email and password are required.'
            },
            status=status.HTTP_400_BAD_REQUEST
        )

    # Use the service to login the user
    service = AccountService()
    service_response = service.login_user(email, password)

    return Response(
        {
            'success': service_response.success,
            'message': service_response.message,
            'data': service_response.data
        },
        status=service_response.status
    )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout_user(request):
    """
    Logout a user by blacklisting their refresh token.

    Expected data:
    - refresh: The refresh token to blacklist
    """
    try:
        refresh_token = request.data.get('refresh')

        if not refresh_token:
            return Response(
                {
                    'success': False,
                    'message': 'Refresh token is required.'
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        # Blacklist the refresh token
        token = RefreshToken(refresh_token)
        token.blacklist()

        return Response(
            {
                'success': True,
                'message': 'Successfully logged out.'
            },
            status=status.HTTP_200_OK
        )
    except Exception as e:
        return Response(
            {
                'success': False,
                'message': f'Error logging out: {str(e)}'
            },
            status=status.HTTP_400_BAD_REQUEST
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_profile(request):
    """
    Get the current user's profile information.
    """
    service = AccountService()
    service_response = service.get_user_profile(request.user.id)

    return Response(
        {
            'success': service_response.success,
            'message': service_response.message,
            'data': service_response.data
        },
        status=service_response.status
    )


@api_view(['PUT', 'PATCH'])
@permission_classes([IsAuthenticated])
def update_user_profile(request):
    """
    Update the current user's profile information.

    Expected data (all optional):
    - first_name: User's first name
    - last_name: User's last name
    - username: User's username
    """
    # Only allow certain fields to be updated
    allowed_fields = ['first_name', 'last_name', 'username']
    update_data = {key: value for key, value in request.data.items() if key in allowed_fields}

    if not update_data:
        return Response(
            {
                'success': False,
                'message': 'No valid fields provided for update.'
            },
            status=status.HTTP_400_BAD_REQUEST
        )

    service = AccountService()
    service_response = service.update_user_profile(request.user.id, update_data)

    return Response(
        {
            'success': service_response.success,
            'message': service_response.message,
            'data': service_response.data
        },
        status=service_response.status
    )
