from django.contrib.auth import authenticate
from rest_framework_simplejwt.tokens import RefreshToken
from core.data_classes import APIResponse
from .repositories import AccountRepository


class AccountService:
    """
    Service for account-related business logic.
    Uses AccountRepository for database operations.
    """
    
    def __init__(self):
        self.repository = AccountRepository()
    
    def register_user(self, user_data) -> APIResponse:
        """
        Register a new user.
        
        Args:
            user_data: Dictionary containing user registration data
            
        Returns:
            APIResponse: Response with success status, message, and user data
        """
        # Make a copy of the user data to avoid modifying the original
        user_data_copy = user_data.copy()
        
        # Remove password2 field as it's not needed for user creation
        if 'password2' in user_data_copy:
            user_data_copy.pop('password2')
        
        # Create user through repository
        repo_response = self.repository.create_user(**user_data_copy)
        
        if not repo_response.success:
            return APIResponse(
                success=False,
                message=repo_response.message,
                status=400
            )
        
        # Get the created user
        user = repo_response.data
        
        # Generate tokens for the user
        refresh = RefreshToken.for_user(user)
        
        return APIResponse(
            success=True,
            message="User registered successfully.",
            data={
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'username': user.username,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                },
                'tokens': {
                    'refresh': str(refresh),
                    'access': str(refresh.access_token),
                }
            },
            status=201
        )
    
    def login_user(self, email, password) -> APIResponse:
        """
        Authenticate a user and generate tokens.
        
        Args:
            email: User's email
            password: User's password
            
        Returns:
            APIResponse: Response with success status, message, and tokens
        """
        # Authenticate user
        user = authenticate(username=email, password=password)
        
        if not user:
            return APIResponse(
                success=False,
                message="Invalid credentials.",
                status=401
            )
        
        # Generate tokens
        refresh = RefreshToken.for_user(user)
        
        return APIResponse(
            success=True,
            message="Login successful.",
            data={
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'username': user.username,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                },
                'tokens': {
                    'refresh': str(refresh),
                    'access': str(refresh.access_token),
                }
            },
            status=200
        )
    
    def get_user_profile(self, user_id) -> APIResponse:
        """
        Get a user's profile information.
        
        Args:
            user_id: ID of the user
            
        Returns:
            APIResponse: Response with success status, message, and user data
        """
        repo_response = self.repository.get_user_by_id(user_id)
        
        if not repo_response.success:
            return APIResponse(
                success=False,
                message=repo_response.message,
                status=404
            )
        
        user = repo_response.data
        
        return APIResponse(
            success=True,
            data={
                'id': user.id,
                'email': user.email,
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
            },
            status=200
        )
    
    def update_user_profile(self, user_id, update_data) -> APIResponse:
        """
        Update a user's profile information.
        
        Args:
            user_id: ID of the user to update
            update_data: Dictionary containing fields to update
            
        Returns:
            APIResponse: Response with success status, message, and updated user data
        """
        repo_response = self.repository.update_user(user_id, **update_data)
        
        if not repo_response.success:
            return APIResponse(
                success=False,
                message=repo_response.message,
                status=400 if "not found" not in repo_response.message else 404
            )
        
        user = repo_response.data
        
        return APIResponse(
            success=True,
            message="Profile updated successfully.",
            data={
                'id': user.id,
                'email': user.email,
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
            },
            status=200
        )
