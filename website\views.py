from django.shortcuts import render
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import DjangoModelPermissions, IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from .services import WebsiteService
from .serializers import ContactFormSerializer, AppointmentFormSerializer


class HasContactFormPermission(DjangoModelPermissions):
    """
    Custom permission class that checks if the user has specific permissions for contact forms.
    Extends DjangoModelPermissions to use Django's built-in permission system.
    """
    # Override the default perms_map to customize permission requirements
    perms_map = {
        'GET': ['%(app_label)s.view_contactform'],
        'OPTIONS': [],
        'HEAD': [],
        'POST': ['%(app_label)s.add_contactform'],
        'DELETE': ['%(app_label)s.delete_contactform'],
    }

    def has_permission(self, request, view):
        if view is None or not hasattr(view, 'queryset'):
            from .models import ContactForm
            if view is None:
                class SimpleView:
                    pass
                view = SimpleView()
            view.queryset = ContactForm.objects.all()

        return super().has_permission(request, view)


class HasAppointmentFormPermission(DjangoModelPermissions):
    """
    Custom permission class that checks if the user has specific permissions for appointment forms.
    Extends DjangoModelPermissions to use Django's built-in permission system.
    """
    # Override the default perms_map to customize permission requirements
    perms_map = {
        'GET': ['%(app_label)s.view_appointmentform'],
        'OPTIONS': [],
        'HEAD': [],
        'POST': ['%(app_label)s.add_appointmentform'],
        'DELETE': ['%(app_label)s.delete_appointmentform'],
    }

    def has_permission(self, request, view):
        if view is None or not hasattr(view, 'queryset'):
            from .models import AppointmentForm
            if view is None:
                class SimpleView:
                    pass
                view = SimpleView()
            view.queryset = AppointmentForm.objects.all()

        return super().has_permission(request, view)


@api_view(['POST', 'GET'])
def contact_form_handler(request):
    """
    Handle contact form operations:
    - POST: Create a new contact form entry (public)
    - GET: Retrieve all contact form entries (admin only)
    """
    if request.method == 'GET':
        # Check authentication for GET requests
        if not request.user.is_authenticated:
            return Response(
                {
                    'success': False,
                    'message': 'Authentication required.'
                },
                status=status.HTTP_401_UNAUTHORIZED
            )

        # Check permissions for GET requests
        permission = HasContactFormPermission()
        if not permission.has_permission(request, None):
            return Response(
                {
                    'success': False,
                    'message': 'You do not have permission to perform this action.'
                },
                status=status.HTTP_403_FORBIDDEN
            )

    if request.method == 'POST':
        serializer = ContactFormSerializer(data=request.data)
        if serializer.is_valid():
            service_response = WebsiteService().create_contact_form(serializer.validated_data)

            if service_response.success:
                serializer = ContactFormSerializer(service_response.data)
                return Response(
                    {
                        'success': service_response.success,
                        'message': service_response.message,
                        'data': serializer.data
                    },
                    status=service_response.status
                )
            else:
                return Response(
                    {
                        'success': service_response.success,
                        'message': service_response.message,
                        'errors': service_response.errors
                    },
                    status=service_response.status
                )
        else:
            return Response(
                {
                    'success': False,
                    'message': 'Invalid data provided.',
                    'errors': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )
    elif request.method == 'GET':
        service_response = WebsiteService().get_all_contact_form_entries()

        if service_response.success and service_response.data:
            serializer = ContactFormSerializer(service_response.data, many=True)
            data = serializer.data
        else:
            data = service_response.data

        return Response(
            {
                'success': service_response.success,
                'message': service_response.message,
                'data': data
            },
            status=service_response.status
        )


@api_view(['GET', 'DELETE'])
@permission_classes([IsAuthenticated, HasContactFormPermission])
def contact_form_detail(request, contact_form_id):
    """
    Handle contact form detail operations:
    - GET: Retrieve a specific contact form entry
    - DELETE: Delete a specific contact form entry
    """
    if request.method == 'GET':
        # Get a contact form entry by id
        service_response = WebsiteService().get_contact_form_by_id(contact_form_id)

        # Serialize the data
        if service_response.success and service_response.data:
            serializer = ContactFormSerializer(service_response.data)
            data = serializer.data
        else:
            data = service_response.data

        return Response(
            {
                'success': service_response.success,
                'message': service_response.message,
                'data': data
            },
            status=service_response.status
        )

    elif request.method == 'DELETE':
        # Delete a contact form entry
        service_response = WebsiteService().delete_contact_form(contact_form_id)

        return Response(
            {
                'success': service_response.success,
                'message': service_response.message
            },
            status=service_response.status
        )


# Appointment Form Views
@api_view(['POST', 'GET'])
def appointment_form_handler(request):
    """
    Handle appointment form operations:
    - POST: Create a new appointment form entry (public)
    - GET: Retrieve all appointment form entries (admin only)
    """
    if request.method == 'GET':
        # Check authentication for GET requests
        if not request.user.is_authenticated:
            return Response(
                {
                    'success': False,
                    'message': 'Authentication required.'
                },
                status=status.HTTP_401_UNAUTHORIZED
            )

        # Check permissions for GET requests
        permission = HasAppointmentFormPermission()
        if not permission.has_permission(request, None):
            return Response(
                {
                    'success': False,
                    'message': 'You do not have permission to perform this action.'
                },
                status=status.HTTP_403_FORBIDDEN
            )

    if request.method == 'POST':
        serializer = AppointmentFormSerializer(data=request.data)
        if serializer.is_valid():
            service_response = WebsiteService().create_appointment_form(serializer.validated_data)

            if service_response.success:
                serializer = AppointmentFormSerializer(service_response.data)
                return Response(
                    {
                        'success': service_response.success,
                        'message': service_response.message,
                        'data': serializer.data
                    },
                    status=service_response.status
                )
            else:
                return Response(
                    {
                        'success': service_response.success,
                        'message': service_response.message,
                        'errors': service_response.errors
                    },
                    status=service_response.status
                )
        else:
            return Response(
                {
                    'success': False,
                    'message': 'Invalid data provided.',
                    'errors': serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )
    elif request.method == 'GET':
        service_response = WebsiteService().get_all_appointment_form_entries()

        if service_response.success and service_response.data:
            serializer = AppointmentFormSerializer(service_response.data, many=True)
            data = serializer.data
        else:
            data = service_response.data

        return Response(
            {
                'success': service_response.success,
                'message': service_response.message,
                'data': data
            },
            status=service_response.status
        )


@api_view(['GET', 'DELETE'])
@permission_classes([IsAuthenticated, HasAppointmentFormPermission])
def appointment_form_detail(request, appointment_form_id):
    """
    Handle appointment form detail operations:
    - GET: Retrieve a specific appointment form entry
    - DELETE: Delete a specific appointment form entry
    """
    if request.method == 'GET':
        # Get an appointment form entry by its ID
        service_response = WebsiteService().get_appointment_form_by_id(appointment_form_id)

        # Serialize the data
        if service_response.success and service_response.data:
            serializer = AppointmentFormSerializer(service_response.data)
            data = serializer.data
        else:
            data = service_response.data

        return Response(
            {
                'success': service_response.success,
                'message': service_response.message,
                'data': data
            },
            status=service_response.status
        )

    elif request.method == 'DELETE':
        # Delete an appointment form entry
        service_response = WebsiteService().delete_appointment_form(appointment_form_id)

        return Response(
            {
                'success': service_response.success,
                'message': service_response.message
            },
            status=service_response.status
        )
