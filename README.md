# nthe

New Therapeutic Healthcare Environment

## Table of Contents

- [API Documentation](#api-documentation)
     - [API Endpoint Structure](#api-endpoint-structure)
     - [URL Naming Conventions](#url-naming-conventions)
     - [Application Architecture](#application-architecture)
     - [Example Workflow](#example-workflow)
     - [Data Classes](#data-classes)
     - [Benefits of This Structure](#benefits-of-this-structure)
     - [Notes](#notes)
     - [Authentication and Authorization](#authentication-and-authorization)
     - [Testing](#testing)
     - [Deployment](#deployment)
     - [Contribution Guidelines](#contribution-guidelines)
     - [License](#license)

## API Documentation

This project follows a RESTful API structure with the following conventions:

### API Endpoint Structure

- **List and Create**: `api/<app_name>/<collection>`
     - Methods: `GET` (list), `POST` (create)
- **Detail and Modify**: `api/<app_name>/<collection>/<item_id>`
     - Methods: `GET` (retrieve), `PUT` (update), `DELETE` (delete), `PATCH` (partial update), `POST` (custom actions)

#### Examples:

- `api/accounts/` (List and create accounts)
- `api/accounts/2/` (Retrieve, update, or delete account with ID 2)

### URL Naming Conventions

- Use kebab-case for multi-word URLs (e.g., `urls-with-multiple-rows` instead of `urls_with_multiple_rows`).

### Application Architecture

This project follows an **nth-tier application structure** to ensure scalability, maintainability, and separation of concerns. The tiers are as follows:

1. **Repositories**:

      - Responsible for direct CRUD operations with the database.
      - Encapsulate database logic to keep it separate from business logic.

2. **Services**:

      - Use repositories to implement business logic.
      - Act as an intermediary between repositories and views.

3. **Views**:
      - Function-based views (FBVs) are used to interact with services.
      - Handle HTTP requests and responses.

### Example Workflow

1. **Repository**: Handles database queries for the `accounts` app.

      ```python
      class AccountRepository:
          def get_all_accounts(self) -> RepositoryResponse:
              # Query logic here
              pass
      ```

2. **Service**: Implements business logic using the repository.

      ```python
      class AccountService:
          def list_accounts(self) -> APIResponse:
              return AccountRepository().get_all_accounts()
      ```

3. **View**: Interacts with the service to handle API requests.

      ```python
      from rest_framework.response import Response


      def account_list(request):
          accounts = AccountService().list_accounts()
          return Response(accounts, safe=False)
      ```

### Data Classes

To standardize responses across the application, we use the following data classes:

#### RepositoryResponse

Used by repositories to return data from CRUD operations.

```python
from dataclasses import dataclass
from typing import Any, Optional

@dataclass
class RepositoryResponse:
    success: bool
    message: Optional[str] = None
    data: Optional[Any] = None
```

#### APIResponse

Used by services to return data to views.

```python
from dataclasses import dataclass
from typing import Any, Optional

@dataclass
class APIResponse:
    success: bool
    message: Optional[str] = None
    data: Optional[Any] = None
    errors: Optional[Any] = None
    status: Optional[int] = 400
```

These data classes are defined in the `core/data_classes.py` file and ensure consistency in how data is passed between layers.

### Benefits of This Structure

- **Separation of Concerns**: Each layer has a distinct responsibility.
- **Scalability**: Easy to extend functionality without affecting other layers.
- **Testability**: Each layer can be tested independently.

### Notes

- Ensure proper error handling and validation at each layer.
- Follow Django's best practices for security and performance.
- Insure that protected views are protected by adding a permission decorator and authentication class (IsAuthenticated)
- Ensure authentication and authorization is handled carefully

### Authentication and Authorization

- Protected views use the `IsAuthenticated` permission class.
- JWT tokens are used for authentication, with access and refresh tokens managed by `SimpleJWT`.

### Testing

- Run tests using the following command:
     ```bash
     python manage.py test
     ```
- Ensure each layer (repositories, services, views) has its own test cases.

### Deployment

- Set `ENVIRONMENT=production` in the `.env` file.
- Configure the database URL and allowed hosts.
- Use `gunicorn` as the WSGI server:
     ```bash
     gunicorn core.wsgi:application --bind 0.0.0.0:8000
     ```

### Contribution Guidelines

- Fork the repository and create a new branch for your feature or bug fix.
- Ensure all tests pass before submitting a pull request.
- Follow the coding standards outlined in the project.

### License

This project is licensed under the MIT License. See the LICENSE file for details.
