from django.http import JsonResponse
from rest_framework_simplejwt.tokens import AccessToken
from rest_framework_simplejwt.exceptions import TokenError
from rest_framework_simplejwt.token_blacklist.models import BlacklistedToken, OutstandingToken
import jwt
from django.conf import settings


class TokenVerificationMiddleware:
    """
    Middleware to verify that JWT tokens are not blacklisted.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Skip middleware for non-API requests or paths that don't need authentication
        if not request.path.startswith('/api/') or request.path.startswith('/api/accounts/login') or \
           request.path.startswith('/api/accounts/register') or request.path.startswith('/api/accounts/token'):
            return self.get_response(request)
        
        # Check for Authorization header
        auth_header = request.headers.get('Authorization', '')
        if not auth_header.startswith('Bearer '):
            return self.get_response(request)
        
        # Extract token
        token = auth_header.split(' ')[1]
        
        try:
            # Decode token to get its JTI (JWT ID)
            decoded_token = jwt.decode(
                token, 
                settings.SECRET_KEY, 
                algorithms=["HS256"],
                options={"verify_signature": True}
            )
            
            # Check if token is blacklisted
            token_jti = decoded_token.get('jti')
            user_id = decoded_token.get('user_id')
            
            # Look for a matching outstanding token that's been blacklisted
            outstanding_token = OutstandingToken.objects.filter(
                jti=token_jti,
                user_id=user_id
            ).first()
            
            if outstanding_token and BlacklistedToken.objects.filter(token=outstanding_token).exists():
                return JsonResponse({
                    'success': False,
                    'message': 'Token has been blacklisted. Please log in again.'
                }, status=401)
                
        except (jwt.PyJWTError, TokenError):
            # If there's an error decoding the token, let the regular authentication handle it
            pass
        
        return self.get_response(request)
