# Generated by Django 5.2.1 on 2025-05-12 14:33

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ContactForm',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.<PERSON>r<PERSON><PERSON>(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('phone_number', models.Char<PERSON>ield(max_length=20)),
                ('subject', models.Char<PERSON>ield(max_length=100)),
                ('message', models.TextField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
