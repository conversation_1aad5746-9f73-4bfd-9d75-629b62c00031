from django.db import migrations
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType


def create_permissions_and_groups(apps, schema_editor):
    """
    Create permissions and groups for the ContactForm model.
    """
    # Get the ContentType for the ContactForm model
    ContactForm = apps.get_model('website', 'ContactForm')
    content_type = ContentType.objects.get_for_model(ContactForm)

    # Create Admin group if it doesn't exist
    admin_group, created = Group.objects.get_or_create(name='Admin')

    # Get or create all permissions for the ContactForm model
    view_permission, _ = Permission.objects.get_or_create(
        codename='view_contactform',
        defaults={'name': 'Can view contact form'},
        content_type=content_type
    )
    add_permission, _ = Permission.objects.get_or_create(
        codename='add_contactform',
        defaults={'name': 'Can add contact form'},
        content_type=content_type
    )
    change_permission, _ = Permission.objects.get_or_create(
        codename='change_contactform',
        defaults={'name': 'Can change contact form'},
        content_type=content_type
    )
    delete_permission, _ = Permission.objects.get_or_create(
        codename='delete_contactform',
        defaults={'name': 'Can delete contact form'},
        content_type=content_type
    )

    # Add permissions to the Admin group
    admin_group.permissions.add(view_permission)
    admin_group.permissions.add(add_permission)
    admin_group.permissions.add(change_permission)
    admin_group.permissions.add(delete_permission)


def remove_permissions_and_groups(apps, schema_editor):
    """
    Remove permissions and groups for the ContactForm model.
    """
    # Get the Admin group
    try:
        admin_group = Group.objects.get(name='Admin')

        # Get the ContentType for the ContactForm model
        ContactForm = apps.get_model('website', 'ContactForm')
        content_type = ContentType.objects.get_for_model(ContactForm)

        # Get all permissions for the ContactForm model
        permissions = Permission.objects.filter(content_type=content_type)

        # Remove permissions from the Admin group
        for permission in permissions:
            admin_group.permissions.remove(permission)
    except Group.DoesNotExist:
        pass


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(create_permissions_and_groups, remove_permissions_and_groups),
    ]
