from django.contrib.auth import get_user_model
from core.data_classes import RepositoryResponse
from django.db import IntegrityError

User = get_user_model()


class AccountRepository:
    """
    Repository for account-related database operations.
    Handles CRUD operations for the User model.
    """
    
    def create_user(self, **user_data) -> RepositoryResponse:
        """
        Create a new user in the database.
        
        Args:
            **user_data: User data including email, username, password, etc.
            
        Returns:
            RepositoryResponse: Response with success status and user data or error message
        """
        try:
            # Check if user with this email already exists
            if User.objects.filter(email=user_data.get('email')).exists():
                return RepositoryResponse(
                    success=False,
                    message="User with this email already exists."
                )
            
            # Create the user
            user = User.objects.create_user(**user_data)
            
            return RepositoryResponse(
                success=True,
                message="User created successfully.",
                data=user
            )
        except IntegrityError as e:
            return RepositoryResponse(
                success=False,
                message=f"Failed to create user: {str(e)}"
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                message=f"An error occurred: {str(e)}"
            )
    
    def get_user_by_id(self, user_id) -> RepositoryResponse:
        """
        Get a user by their ID.
        
        Args:
            user_id: The ID of the user to retrieve
            
        Returns:
            RepositoryResponse: Response with success status and user data or error message
        """
        try:
            user = User.objects.get(id=user_id)
            return RepositoryResponse(
                success=True,
                data=user
            )
        except User.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="User not found."
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                message=f"An error occurred: {str(e)}"
            )
    
    def get_user_by_email(self, email) -> RepositoryResponse:
        """
        Get a user by their email.
        
        Args:
            email: The email of the user to retrieve
            
        Returns:
            RepositoryResponse: Response with success status and user data or error message
        """
        try:
            user = User.objects.get(email=email)
            return RepositoryResponse(
                success=True,
                data=user
            )
        except User.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="User not found."
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                message=f"An error occurred: {str(e)}"
            )
    
    def update_user(self, user_id, **update_data) -> RepositoryResponse:
        """
        Update a user's information.
        
        Args:
            user_id: The ID of the user to update
            **update_data: The data to update
            
        Returns:
            RepositoryResponse: Response with success status and updated user data or error message
        """
        try:
            user = User.objects.get(id=user_id)
            
            # Update user fields
            for key, value in update_data.items():
                if hasattr(user, key):
                    setattr(user, key, value)
            
            user.save()
            
            return RepositoryResponse(
                success=True,
                message="User updated successfully.",
                data=user
            )
        except User.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="User not found."
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                message=f"An error occurred: {str(e)}"
            )
